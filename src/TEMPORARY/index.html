<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aletheia - Software Solutions (Light Theme)</title>
    <!-- Removed reference to non-existent light-theme-style.css -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
</head>
<body>
<style>
    /* ===================================================================
   ALETHEIA - CONSOLIDATED STYLES (LIGHT THEME)
   Modern Software Solutions Website
   Light Theme with Dynamic Starry Background Variable
   =================================================================== */

/* ===================================================================
   CSS VARIABLES & ROOT STYLES
   =================================================================== */
:root {
    /* New Light Theme Color Palette */
    --primary-accent: #5E6666;      /* Dark gray-cyan for primary interactive elements */
    --secondary-accent: #AEC3C3;    /* Muted light cyan/gray for secondary accents */
    --background-light: #FFFFFF;    /* Main white background */
    --background-soft: #F8F9FA;     /* Soft off-white for alternate sections */
    --text-dark: #333737;           /* Main dark text color for readability */
    --text-light: #F2F2F2;          /* Light gray for text on dark backgrounds */
    --border-color: #E0E0E0;        /* Light gray for borders and dividers */

    /* Glassmorphism for Light Theme */
    --glass-bg: rgba(255, 255, 255, 0.6);
    --glass-blur: blur(10px);

    /* DYNAMIC STARRY BACKGROUND VARIABLE */
    --starry-background:
        radial-gradient(1px 1px at 50px 100px, #888, transparent),
        radial-gradient(1px 1px at 100px 200px, #999, transparent),
        radial-gradient(1.5px 1.5px at 250px 350px, #aaa, transparent);

    /* Typography */
    --font-primary: 'Inter', 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

    /* Responsive breakpoints */
    --mobile: 768px;
    --tablet: 1024px;
    --desktop: 1200px;
}

/* Utility: Starry background for dark sections only */
.starfield {
    background-image:
        var(--starry-background),
        linear-gradient(to bottom, #2a2f2f, #1a1a1a);
    background-size:
        400px 400px,
        300px 300px,
        600px 600px,
        100% 100%;
    background-repeat: repeat, repeat, repeat, no-repeat;
    background-attachment: fixed;
}

/* ===================================================================
   BASE STYLES & RESETS
   =================================================================== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: var(--font-primary);
    background-color: var(--background-light);
    color: var(--text-dark);
    line-height: 1.6;
    overflow-x: hidden;
    font-feature-settings: 'rlig' 1, 'calt' 1;
}

/* Accessibility improvements */
button, 
.cta-button, 
.mas-info-btn, 
.form-button,
.nav-link {
    min-height: 44px;
    min-width: 44px;
}

button:focus,
.cta-button:focus,
.mas-info-btn:focus,
.form-button:focus,
.nav-link:focus,
.form-input:focus {
    outline: 2px solid var(--primary-accent);
    outline-offset: 2px;
}

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===================================================================
   HEADER
   =================================================================== */
.aurora-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 70px;
    z-index: 1000;
    transition: all 0.3s ease;
    backdrop-filter: var(--glass-blur);
    background-color: #FFFFFF;
    border-bottom: 1px solid var(--border-color);
}

/* Transparent state over hero */
.aurora-header.is-transparent {
    background-color: transparent;
    border-bottom-color: transparent;
}

.aurora-header.is-transparent .nav-link {
    color: #FFFFFF;
}

.aurora-header.is-transparent .nav-link::before {
    background: #FFFFFF;
}

.aurora-header.is-transparent .nav-highlight {
    color: #FFFFFF;
    border-color: rgba(255, 255, 255, 0.8);
}

.aurora-header.is-transparent .mobile-menu-toggle span {
    background-color: #FFFFFF;
}

/* Spacer shown only when header is solid to avoid layout jump */
.header-spacer {
    height: 70px;
    display: none;
}

.aurora-header.is-solid + .header-spacer {
    display: block;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

.logo {
    height: 40px;
    width: auto;
}

.nav-menu {
    display: flex;
    gap: 2.5rem;
    align-items: center;
}

.nav-link {
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-dark);
    text-decoration: none;
    padding: 0.5rem 0;
    position: relative;
    transition: all 0.3s ease;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-accent);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::before {
    width: 100%;
}

.nav-highlight {
    color: var(--text-dark);
    padding: 0.6rem 1.2rem;
    border-radius: 6px;
    font-weight: 600;
    border: 1px solid var(--primary-accent);
    border-bottom: 2px solid var(--primary-accent);
}

.nav-highlight:hover {
    transform: translateY(-2px);
    border-bottom: 2px solid var(--primary-accent);
}

.nav-highlight::before {
    display: none;
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 1001;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--text-dark);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* ===================================================================
   HERO SECTION
   =================================================================== */
.hero {
    height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: 0 2rem;
    border-bottom: 1px solid var(--border-color);
    margin-top: 0; /* Header overlays hero when transparent */
}


.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    z-index: 0;
}

.hero-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
    transform: scale(1.5);
}

.hero-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    gap: 3rem;
    position: relative;
    z-index: 1;
    padding: 2rem 0;
}

.hero-left {
    flex: 1;
    text-align: left;
    max-width: 45%;
    padding-right: 1rem;
}

.hero-right {
    flex: 1;
    max-width: 50%;
    padding-left: 1rem;
}

.video-frame {
    width: 100%;
    padding-top: 56.25%;
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(0, 0, 0, 0.05);
    margin-bottom: 1rem;
}

.right-description {
    font-size: 0.9rem;
    color: #FFFFFF;
    text-align: right;
    width: 100%;
    margin-top: 1rem;
    opacity: 0.8;
    padding: 0.5rem 0;
}

.hero-title {
    font-size: 3.2rem;
    font-weight: 800;
    color: #FFFFFF;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    text-transform: uppercase;
    word-wrap: break-word;
    hyphens: auto;
}

.ai-3d {
    position: relative;
    display: inline-block;
    color: #FFFFFF;
    text-shadow:
        0 2px 0 var(--secondary-accent),
        0 10px 24px rgba(0, 0, 0, 0.35);
    transform: translateZ(0);
    transition: text-shadow 250ms ease, transform 250ms ease;
}

/* Optional subtle emphasis on hover/focus */
.ai-3d:hover,
.ai-3d:focus {
    transform: translateY(-1px);
    text-shadow:
        0 3px 0 var(--secondary-accent),
        0 12px 28px rgba(0, 0, 0, 0.4);
}

.hero-description {
    font-size: 1.2rem;
    color: #FFFFFF;
    line-height: 1.6;
    margin-bottom: 2.5rem;
    font-weight: 400;
    opacity: 0.9;
    display: inline-block;
    max-width: 100%;
    word-wrap: break-word;
}

.hero-cta-buttons {
    display: flex;
    gap: 1.2rem;
    flex-wrap: wrap;
}

.cta-button {
    display: inline-block;
    padding: 0.9rem 1.8rem;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
    white-space: nowrap;
    min-width: 140px;
    color: #1a1a1a;
    border: 2px solid transparent;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(174, 195, 195, 0.3);
}

.cta-primary {
    background: transparent;
    color: #FFFFFF;
    border: 2px solid #FFFFFF;
}

.cta-secondary {
    background: transparent;
    color: #FFFFFF;
    border: 2px solid #FFFFFF;
}

.cta-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* ===================================================================
   GENERAL SECTION STYLING
   =================================================================== */
.section {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 4rem 2rem;
    min-height: auto;
}

.content-wrapper {
    width: 100%;
    max-width: 1200px;
    padding: 0 1rem;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    padding: 0 1rem;
}

.section-title h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.8rem;
    text-transform: uppercase;
}

.section-title p {
    font-size: 1.1rem;
    color: var(--text-dark);
    opacity: 0.8;
}

/* ===================================================================
   METHODOLOGY SECTION (#nosotros)
   =================================================================== */
#nosotros {
    background-color: var(--background-soft);
    border-bottom: 1px solid var(--border-color);
    padding: 5rem 2rem;
}

.methodology-steps-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3rem;
    margin-top: 3rem;
    padding: 0 1rem;
}

.methodology-steps-row {
    display: flex;
    justify-content: center;
    align-items: stretch;
    gap: 2rem;
    width: 100%;
    flex-wrap: wrap;
}

.methodology-step {
    border-radius: 16px;
    padding: 2.5rem 1.5rem;
    width: calc(25% - 2rem);
    min-width: 250px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    transition: all 0.3s ease;
    background: var(--background-light);
    border: 1px solid var(--border-color);
    min-height: 360px;
    margin-bottom: 1rem;
}
.process-icon {
    width: 80px;
    height: 80px;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.step-text {
    font-size: 1.1rem;
    color: var(--text-dark);
    line-height: 1.5;
    margin-top: 1.5rem;
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.philosophy-intro {
    max-width: 800px;
    text-align: center;
    margin: 0 auto 3rem auto;
    font-size: 1.1rem;
    opacity: 0.9;
    padding: 0 1rem;
    line-height: 1.6;
}

/* ===================================================================
   NETWORK VISUALIZATION SECTION
   =================================================================== */
.network-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin: 3rem 0;
    padding: 2rem 1rem;
    position: relative;
    overflow: hidden;
    min-height: 400px;
}

.network-description {
    order: 2;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    padding: 0 1rem;
}

.network-visualization {
    order: 1;
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.network-description h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    line-height: 1.2;
    text-align: left;
}

.network-intro {
    font-size: 1.2rem;
    line-height: 1.6;
    color: #e0e0e0;
    margin-bottom: 2rem;
    text-align: left;
}

#network-canvas {
    display: block;
    margin: 0 auto;
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    background-color: transparent;
}

.canvas-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

/* ===================================================================
   BUSINESS SHOWCASE SECTION
   =================================================================== */
.business-showcase {
    padding: 2rem 0;

}

.business-blocks {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.business-block {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem;
    background: var(--background-light);
}


.business-svg-icon {
    width: 70px;
    height: 70px;
    object-fit: contain;
    transition: all 0.3s ease;
}

.business-content {
    flex: 1;
}

.business-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 3px solid var(--secondary-accent);
    display: inline-block;
}

.business-content p {
    color: var(--text-dark);
    font-size: 0.95rem;
    line-height: 1.6;
    margin: 0;
}

/* ===================================================================
   SOLUCIONES SECTION
   =================================================================== */
#soluciones {
    background-color: #333737;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    padding: 5rem 2rem;
    position: relative;
    overflow: hidden;
}

#soluciones .section-title h2,
#soluciones .section-title p {
    color: var(--text-light);
}

#soluciones .section-title {
    position: relative;
    z-index: 1;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    width: 100%;
    padding: 0 1rem;
    position: relative;
    z-index: 1;
}

.product-card {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 3rem 2rem;
    transition: all 0.4s ease;
    background: rgba(51, 55, 55, 0.6);
    backdrop-filter: blur(10px);
    color: var(--text-light);
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.card-title h3 {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--text-light);
    text-transform: uppercase;
    text-align: center;
    margin-bottom: 1.5rem;
}

.card-description p {
    font-size: 0.95rem;
    color: var(--text-light);
    line-height: 1.6;
    opacity: 0.8;
    text-align: center;
    margin-bottom: 2rem;
}

.card-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.card-list li {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 0.8rem;
    padding-left: 1.5rem;
    position: relative;
    opacity: 0.9;
    line-height: 1.4;
}

.card-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--secondary-accent);
    font-weight: bold;
}

.mas-info-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: transparent;
    color: var(--text-light);
    text-decoration: none;
    border: 2px solid var(--secondary-accent);
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    margin-top: 1.5rem;
    align-self: center;
    position: relative;
    overflow: hidden;
}

.mas-info-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(174, 195, 195, 0.2), transparent);
    transition: left 0.5s ease;
}

.mas-info-btn:hover {
    background: var(--secondary-accent);
    color: var(--text-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(174, 195, 195, 0.3);
    border-color: var(--secondary-accent);
}

.mas-info-btn:hover::before {
    left: 100%;
}

.mas-info-btn i {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.mas-info-btn:hover i {
    transform: translateX(3px);
}

/* Disabled button for cards (e.g., features in development) */
.disabled-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: rgba(255, 255, 255, 0.08);
    color: rgba(242, 242, 242, 0.7);
    text-decoration: none;
    border: 2px dashed rgba(174, 195, 195, 0.4);
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: none;
    margin-top: 1.5rem;
    align-self: center;
    position: relative;
    cursor: not-allowed;
    pointer-events: none;
    user-select: none;
    opacity: 0.7;
}

.disabled-btn i {
    font-size: 1.1rem;
}

.disabled-btn:hover,
.disabled-btn:focus {
    transform: none;
    box-shadow: none;
    outline: none;
}

/* ===================================================================
   LOGO CAROUSEL
   =================================================================== */
.logo-carousel-section {
    background-color: var(--background-soft);
    border-bottom: 1px solid var(--border-color);
    padding: 4rem 2rem;
}

.logo-carousel-section .section-title p {
    font-size: 1rem;
    font-weight: 500;
    letter-spacing: 2px;
    text-transform: uppercase;
    opacity: 0.7;
    margin-bottom: 2rem;
}

.carousel-container {
    width: 100%;
    overflow: hidden;
    padding: 2rem 0;
}

.carousel-track {
    display: flex;
    animation: scroll 40s linear infinite;
    will-change: transform;
    align-items: center;
}

@keyframes scroll {
    from { transform: translateX(0); }
    to { transform: translateX(-50%); }
}

.carousel-slide {
    padding: 0 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-slide img {
    height: 60px;
    width: auto;
    max-width: 150px;
    object-fit: contain;
    filter: grayscale(100%) contrast(0%) brightness(1.2);
    opacity: 0.6;
    transition: all 0.3s ease;
}

.carousel-slide img:hover {
    filter: none;
    opacity: 1;
}

/* ===================================================================
   CONTACT SECTION
   =================================================================== */
#contacto {
    background-color: var(--background-soft);
    padding: 5rem 2rem;
}

.contact-card {
    display: flex;
    width: 100%;
    max-width: 1000px;
    border-radius: 16px;
    overflow: hidden;
    background: var(--background-light);
    border: 1px solid var(--border-color);
    box-shadow: 0 10px 30px rgba(0,0,0,0.05);
    margin: 0 auto;
}

.social-panel {
    width: 40%;
    padding: 3rem 2rem;
    background-color: #F1F3F5;
}

.social-panel h3 {
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.social-panel p {
    font-size: 0.95rem;
    margin-bottom: 2rem;
    opacity: 0.8;
    line-height: 1.5;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 1.2rem;
    padding: 1rem 1.2rem;
    border-radius: 8px;
    color: var(--text-dark);
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
    background: rgba(0, 0, 0, 0.05);
}

.social-link:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: translateX(5px);
}

.social-link i {
    font-size: 1.2rem;
}

.form-panel {
    width: 60%;
    padding: 3rem 2rem;
}

.form-panel h3 {
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.8rem;
    opacity: 0.8;
}

.form-input {
    width: 100%;
    padding: 1.2rem;
    border-radius: 8px;
    color: var(--text-dark);
    font-size: 1rem;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    background: var(--background-soft);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-accent);
    background: var(--background-light);
    box-shadow: 0 0 0 3px rgba(94, 102, 102, 0.1);
}

.form-button {
    width: 100%;
    padding: 1.2rem;
    border: none;
    border-radius: 8px;
    background: var(--primary-accent);
    color: var(--text-light);
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.form-button:hover {
    background: #4a5050;
    transform: translateY(-2px);
}

/* ===================================================================
   FOOTER
   =================================================================== */
.footer {
    padding: 4rem 2rem 3rem;
    color: var(--text-dark);
    background-color: var(--background-soft);
    border-top: 1px solid var(--border-color);
}

.footer-contenido {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    flex-wrap: wrap;
    gap: 3rem;
    padding: 0 1rem;
}

.footer-col h3 {
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    font-size: 1rem;
    font-weight: 600;
}

.footer-col ul {
    list-style: none;
    padding: 0;
}

.footer-col ul li {
    margin-bottom: 1rem;
    line-height: 1.5;
}

.footer-col ul li a {
    text-decoration: none;
    color: var(--text-dark);
    opacity: 0.8;
    transition: opacity 0.3s ease;
    display: inline-block;
    padding: 0.2rem 0;
}

.footer-col ul li a:hover {
    opacity: 1;
    color: var(--primary-accent);
}

.copy {
    width: 100%;
    max-width: 1200px;
    margin: 3rem auto 0 auto;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
    opacity: 0.7;
    padding-left: 1rem;
    padding-right: 1rem;
}

/* ===================================================================
   RESPONSIVE DESIGN
   =================================================================== */

/* Tablet and smaller desktops */
@media (max-width: 1024px) {
    .product-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    .product-card {
        max-width: 450px;
        margin: 0 auto;
        min-height: 350px;
    }
    .mas-info-btn {
        padding: 0.7rem 1.3rem;
        font-size: 0.85rem;
    }
    .hero-container {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }
    .hero-left, .hero-right {
        max-width: 100%;
    }
    .hero-cta-buttons {
        justify-content: center;
    }
    .methodology-steps-row {
        flex-direction: column;
        gap: 1.5rem;
    }
    .methodology-step {
        width: 100%;
        max-width: 400px;
        margin: 0 auto;
        min-height: 300px;
    }
    .business-blocks {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    .business-block {
        padding: 1.5rem;
        gap: 1rem;
    }
    .business-icon {
        width: 70px;
        height: 70px;
    }
    .business-svg-icon {
        width: 55px;
        height: 55px;
    }
    .business-title {
        font-size: 1.3rem;
    }
    .network-section {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
    .network-description h2 {
        font-size: 2.2rem;
    }
    #network-canvas {
        width: 100%;
        max-width: 700px;
    }
}

/* Mobile devices */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: left 0.3s ease;
        z-index: 999;
    }
    /* Ensure readable colors when header is transparent but mobile menu is open */
    .aurora-header.is-transparent .nav-menu,
    .aurora-header.is-transparent .nav-menu.active {
        background: rgba(255, 255, 255, 0.95);
    }
    .aurora-header.is-transparent .nav-menu .nav-link {
        color: var(--text-dark);
    }
    .aurora-header.is-transparent .nav-menu .nav-link::before {
        background: var(--primary-accent);
    }
    .nav-menu.active {
        left: 0;
    }
    .nav-menu .nav-link {
        margin: 1rem 0;
        font-size: 1.2rem;
        padding: 1rem 2rem;
        border-radius: 8px;
        width: 80%;
        text-align: center;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(0, 0, 0, 0.1);
    }
    .aurora-header.is-transparent .nav-menu .nav-highlight {
        background: var(--primary-accent);
        color: #FFFFFF;
        border-color: var(--primary-accent);
    }
    .nav-menu .nav-highlight {
        background: var(--primary-accent);
        color: white;
        border-color: var(--primary-accent);
    }
    .section {
        padding: 3rem 1rem;
    }
    .content-wrapper {
        padding: 0 0.5rem;
    }
    .header-container {
        padding: 0.5rem 1rem;
    }
    .hero {
        /* Ensure hero title clears the fixed header but keeps background under it */
        height: 100svh;
        min-height: 100svh;
        padding: 70px 1rem 0; /* top padding equals header height */
        scroll-margin-top: 70px; /* in-page navigation offset */
    }
    .hero-container {
        padding: 1rem 0;
    }
    .hero-title {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        line-height: 1.1;
    }
    .hero-description {
        font-size: 1rem;
        margin-bottom: 2rem;
        padding: 0 0.5rem;
    }
    .hero-cta-buttons {
        gap: 1rem;
    }
    .cta-button {
        min-width: 140px;
        padding: 0.8rem 1.5rem;
    }
    .methodology-step {
        margin: 0 auto 1rem auto;
        min-height: 280px;
        padding: 2rem 1.5rem;
    }
    .product-grid {
        gap: 2rem;
    }
    .product-card {
        width: 100%;
        max-width: 400px;
        margin: 0 auto 1rem auto;
        min-height: 350px;
        padding: 2rem 1.5rem;
    }
    .mas-info-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.8rem;
        margin-top: 1rem;
    }
    .contact-card {
        flex-direction: column;
        margin: 0 1rem;
    }
    .social-panel, .form-panel {
        width: 100%;
        padding: 2rem 1.5rem;
    }
    .business-blocks {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    .business-block {
        flex-direction: column;
        text-align: center;
    }
    .business-icon {
        width: 60px;
        height: 60px;
    }
    .business-svg-icon {
        width: 50px;
        height: 50px;
    }
    .business-title {
        font-size: 1.2rem;
    }
    .business-content p {
        font-size: 0.9rem;
    }
    .network-section {
        text-align: center;
        padding: 2rem 1rem;
    }
    .network-description {
        order: 2;
        padding: 0 0.5rem;
    }
    .network-visualization {
        order: 1;
        width: 100%;
    }
    .network-description h2 {
        font-size: 2rem;
        text-align: center;
    }
    .network-description p {
        text-align: justify;
    }
    .network-intro {
        font-size: 1.1rem;
        line-height: 1.5;
        padding: 0 0.5rem;
        text-align: center;
    }
    #network-canvas {
        max-width: 600px;
        height: auto;
        margin: 0 auto;
    }
    .canvas-container {
        padding: 0 0.5rem;
        margin: 1.5rem auto;
    }
    .story-values h3 {
        font-size: 1.5rem;
        margin-bottom: 2rem;
    }
    .philosophy-intro {
        font-size: 1rem;
        margin-bottom: 2rem;
        padding: 0 0.5rem;
    }
    .section-title {
        margin-bottom: 2rem;
        padding: 0 0.5rem;
    }
    .section-title h2 {
        font-size: 1.8rem;
    }
    .section-title p {
        font-size: 1rem;
    }
    .footer {
        padding: 3rem 1rem 2rem;
    }
    .footer-contenido {
        gap: 2rem;
        flex-direction: column;
    }
    .copy {
        margin: 2rem auto 0 auto;
        padding: 0 0.5rem;
    }
    .logo-carousel-section {
        padding: 3rem 1rem;
    }
    .carousel-container {
        padding: 1rem 0;
    }
    .carousel-slide {
        padding: 0 20px;
    }
    .carousel-slide img {
        height: 40px;
        max-width: 100px;
    }
    #soluciones, #nosotros, #contacto {
        padding: 3rem 1rem;
    }
}

/* Small mobile devices */
@media (max-width: 480px) {
    .nav-menu .nav-link {
        font-size: 1.1rem;
        padding: 0.8rem 1.5rem;
        width: 90%;
    }
    .section {
        padding: 2rem 0.5rem;
    }
    .content-wrapper {
        padding: 0 0.25rem;
    }
    .hero {
        padding: 0 0.5rem;
        scroll-margin-top: 70px;
    }
    .hero-container {
        gap: 1.5rem;
        padding: 0.5rem 0;
    }
    .hero-title {
        font-size: 2rem;
        margin-bottom: 0.8rem;
    }
    .hero-description {
        font-size: 0.9rem;
        padding: 0 0.25rem;
        margin-bottom: 1.5rem;
    }
    .hero-cta-buttons {
        gap: 0.8rem;
    }
    .cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
        min-width: 120px;
    }
    .methodology-step {
        padding: 1.5rem 1rem;
        min-height: 250px;
        margin-bottom: 0.8rem;
    }
    .step-text {
        font-size: 1rem;
    }
    .product-card {
        padding: 1.5rem 1rem;
        min-height: 320px;
    }
    .mas-info-btn {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
        margin-top: 0.8rem;
    }
    .contact-card {
        margin: 0 0.5rem;
    }
    .social-panel, .form-panel {
        padding: 1.5rem 1rem;
    }
    .form-input, .form-button {
        padding: 1rem;
    }
    .business-block {
        padding: 1.2rem 0.8rem;
    }
    .business-icon {
        width: 50px;
        height: 50px;
    }
    .business-svg-icon {
        width: 45px;
        height: 45px;
    }
    .business-title {
        font-size: 1.1rem;
    }
    .business-content p {
        font-size: 0.85rem;
    }
    .network-section {
        padding: 1.5rem 0.5rem;
        gap: 1.5rem;
    }
    .network-description {
        padding: 0 0.25rem;
    }
    .network-description h2 {
        font-size: 1.6rem;
        margin-bottom: 0.8rem;
    }
    .network-intro {
        font-size: 0.95rem;
        line-height: 1.4;
        padding: 0 0.25rem;
    }
    #network-canvas {
        max-width: 100%;
    }
    .canvas-container {
        padding: 0 0.25rem;
        margin: 1rem auto;
    }
}
</style>
<!-- Aurora Header -->
<header class="aurora-header is-transparent">
    <div class="header-container">
        <div class="logo-container">
            <img src="Media/Logo.svg" alt="Aletheia" class="logo">
        </div>
        <nav class="nav-menu" id="nav-menu">

            <a href="#soluciones" class="nav-link">Soluciones</a>
            <a href="#precios" class="nav-link">Demos</a>
            <a href="education.html" class="nav-link">FAQs</a>
            <a href="#contacto" class="nav-link nav-highlight">Contacto</a>
        </nav>
        <div class="mobile-menu-toggle" id="mobile-menu-toggle">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </div>
</header>
<!-- Spacer that appears only when header becomes solid -->
<div class="header-spacer"></div>

<!-- Hero Section -->
<section class="hero">
    <video class="hero-video" autoplay muted loop playsinline>
        <source src="Media/Untitled video - Made with Clipchamp.mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>
    <div class="hero-container">
        <div class="hero-left">
            <h1 class="hero-title">Soluciones de Software con <span class="ai-3d">Inteligencia Artificial</span></h1>
            <p class="hero-description">Transformamos tu negocio con tecnología de vanguardia que automatiza, optimiza y potencia tus operaciones diarias.</p>
            <div class="hero-cta-buttons">
                <a href="#contacto" class="cta-button cta-primary">Comenzar ahora</a>
                <a href="#soluciones" class="cta-button cta-secondary">Explorar soluciones</a>
            </div>
        </div>
        <div class="hero-right">
            <div class="video-frame">
                <!-- This frame is now empty, acting as a design element -->
            </div>
            <p class="right-description">Descubre cómo nuestra tecnología puede impulsar tu empresa.</p>
        </div>
    </div>
</section>

<!-- Nosotros Section - Work Methodology -->
<section id="nosotros" class="section">
    <div class="content-wrapper">
        <div class="value-content">
            <h4>SOLUCIONES ACCESIBLES PARA TODOS</h4>
            <p>Nuestras soluciones están diseñadas para ser accesibles, tanto en términos de costo como de facilidad de uso, lo que permite a las empresas y estudiantes adoptar tecnología de vanguardia sin la necesidad de grandes inversiones o habilidades técnicas avanzadas.</p>
        </div>
        <div class="methodology-steps-container">
            <div class="methodology-steps-row">
                <div class="methodology-step">
                    <div class="step-icon">
                        <img src="Media/process_icons-01.svg" alt="Análisis de necesidades" class="process-icon">
                    </div>
                    <div class="step-text">Analizamos las  necesidades específicas de cada negocio</div>
                </div>

                <div class="methodology-step">
                    <div class="step-icon">
                        <img src="Media/process_icons-02.svg" alt="Solución a medida" class="process-icon">
                    </div>
                    <div class="step-text">Planeamos una solución eficiente y establecemos el precio del proyecto</div>
                </div>

                <div class="methodology-step">
                    <div class="step-icon">
                        <img src="Media/process_icons-03.svg" alt="Desarrollo e implementación" class="process-icon">
                    </div>
                    <div class="step-text">Desarrollamos e implementamos la solución</div>
                </div>

                <div class="methodology-step">
                    <div class="step-icon">
                        <img src="Media/process_icons-04.svg" alt="Apoyo continuo" class="process-icon">
                    </div>
                    <div class="step-text">Mantenemos la solución actualizada y optimizada</div>
                </div>
            </div>
            
            <p class="philosophy-intro">AletheIA Solutions se distingue por ofrecer soluciones tecnológicas de
                <span style="font-weight: bold;">Inteligencia Artificial</span> altamente especializadas, diseñadas para abordar los desafíos únicos de las PYMEs y el sector educativo en México.</p>
        </div>
    </div>
</section>

<!-- Network Visualization Section -->
<section class="section starfield" style="background-color: #333737; color: white;">
    <div class="content-wrapper">
        <div class="network-section">
            <div class="floating-particles"></div>
            <div class="network-description">
                <h2>Comunicación Inteligente entre tus operaciones</h2>
                <p class="network-intro">Construimos sistemas que se encargan de eficientizar tu procesos, creando una solución
                    única para tu negocio y sus necesidades especificas. Un negocio inteligente toma mejores decisiones
                    y se adapta a las necesidades del mercacado en evolución.
                    <br>
                    <br>
                    <span style="font-weight: bold;">Nosotros te ayudamos.</span>
                </p>
            </div>
            
            <div class="network-visualization">
                <div class="canvas-container">
                    <canvas id="network-canvas" width="1200" height="800"></canvas>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Us Section - Company Story -->
<section class="section">
    <div class="section-title">
        <h2>PARA TODOS LOS SECTORES</h2>
    </div>
    <div class="content-wrapper">
       <div class="business-showcase">
            <div class="business-blocks">
                <div class="business-block">
                    <div class="business-icon">
                        <img src="Media/small_store.svg" alt="Comercio Minorista" class="business-svg-icon">
                    </div>
                    <div class="business-content">
                        <h3 class="business-title">Comercio Minorista</h3>
                        <p>Soluciones integrales para tiendas físicas y online, incluyendo gestión de inventario, punto de venta y análisis de ventas.</p>
                    </div>
                </div>

                <div class="business-block">
                    <div class="business-icon">
                        <img src="Media/sew.svg" alt="Manufactura" class="business-svg-icon">
                    </div>
                    <div class="business-content">
                        <h3 class="business-title">Manufactura</h3>
                        <p>Automatización de procesos productivos, control de calidad y optimización de la cadena de suministro.</p>
                    </div>
                </div>

                <div class="business-block">
                    <div class="business-icon">
                        <img src="Media/lawyer.svg" alt="Servicios Profesionales" class="business-svg-icon">
                    </div>
                    <div class="business-content">
                        <h3 class="business-title">Servicios Profesionales</h3>
                        <p>Herramientas especializadas para consultorías, despachos legales, contables y servicios financieros.</p>
                    </div>
                </div>

                <div class="business-block">
                    <div class="business-icon">
                        <img src="Media/school.svg" alt="Instituciones Educativas" class="business-svg-icon">
                    </div>
                    <div class="business-content">
                        <h3 class="business-title">Instituciones Educativas</h3>
                        <p>Plataformas educativas para diferentes instituciones, de todos los niveles. Con
                            IA generativa para la creación de contenidos, evaluación y seguimiento de alumnos.
                        </p>
                    </div>
                </div>

                <div class="business-block">
                    <div class="business-icon">
                        <img src="Media/hotel.svg" alt="Hospitalidad y Turismo" class="business-svg-icon">
                    </div>
                    <div class="business-content">
                        <h3 class="business-title">Hospitalidad y Turismo</h3>
                        <p>Plataformas de reservas, gestión de hoteles y restaurantes con experiencias personalizadas.</p>
                    </div>
                </div>

                <div class="business-block">
                    <div class="business-icon">
                        <img src="Media/hospital.svg" alt="Salud y Bienestar" class="business-svg-icon">
                    </div>
                    <div class="business-content">
                        <h3 class="business-title">Salud y Bienestar</h3>
                        <p>Sistemas de gestión médica, citas en línea y seguimiento de pacientes con IA.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Soluciones Section - Product Showcase with Starry Background -->
<section id="soluciones" class="section starfield">
    <div class="floating-particles"></div>
    <div class="content-wrapper">
        <div class="section-title">
            <h2>Soluciones</h2>
        </div>

        <div class="product-grid">
            <div class="product-card">
                <div class="card-title"><h3>Escuelas</h3></div>
                <div class="card-description"><p>Soluciones educativas para instituciones de todos los niveles.</p></div>
                <div class="card-list">
                    <ul>
                        <li>Plataforma para alumnos</li>
                        <li>Portal para docentes</li>
                        <li>Seguimiento académico</li>
                        <li>IA para evaluación automática</li>
                        <li>Gestión de tareas y calificaciones</li>
                        <li>Comunicación escuela-familia</li>
                        <li>Reportes personalizados</li>
                        <li>Control de asistencia</li>
                    </ul>
                </div>
                <a href="education.html" class="mas-info-btn">
                    Más información
                    <i class='bx bx-right-arrow-alt'></i>
                </a>
            </div>

            <div class="product-card card-center">
                <div class="card-title"><h3>PyMEs</h3></div>
                <div class="card-description"><p>Impulsa tu pequeña o mediana empresa con nuestras herramientas.</p></div>
                <div class="card-list">
                    <ul>
                        <li>Facturación y contabilidad</li>
                        <li>Gestión de clientes (CRM)</li>
                        <li>IA para análisis de datos</li>
                        <li>Automatización de atención al cliente</li>
                        <li>Inventario inteligente</li>
                        <li>Gestión de ventas y compras</li>
                        <li>Panel de indicadores clave</li>
                        <li>Integración con bancos</li>
                    </ul>
                </div>
                <a href="pymes.html" class="mas-info-btn">
                    Más información
                    <i class='bx bx-right-arrow-alt'></i>
                </a>
            </div>

            <div class="product-card">
                <div class="card-title"><h3>Equipos</h3></div>
                <div class="card-description"><p>Herramientas de colaboración para equipos de alto rendimiento.</p></div>
                <div class="card-list">
                    <ul>
                        <li>Gestión de proyectos</li>
                        <li>Comunicación interna</li>
                        <li>Asistente virtual para reuniones</li>
                        <li>Análisis de productividad</li>
                        <li>Gestión de tareas y deadlines</li>
                        <li>Documentos compartidos</li>
                        <li>Integración con apps externas</li>
                        <li>Panel de desempeño</li>
                    </ul>
                </div>
                <a href="#" class="disabled-btn">
                    En desarrollo
                    <i class='bx bx-lock'></i>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Logo Carousel Section -->
<section class="section logo-carousel-section">
    <div class="content-wrapper">
        <div class="section-title">
            <p>EMPRESAS QUE CONFÍAN EN NUESTROS SERVICIOS</p>
        </div>
        <div class="carousel-container">
            <div class="carousel-track" id="logoTrack">
                <!-- Original slides -->
                <div class="carousel-slide">
                    <img src="Media/cinpro.svg" alt="Cinpro" />
                </div>
                <div class="carousel-slide">
                    <img src="Media/cerveceria.svg" alt="Cervecería" />
                </div>
                <div class="carousel-slide">
                    <img src="Media/csi.svg" alt="CSI" />
                </div>
                <div class="carousel-slide">
                    <img src="Media/toscana.svg" alt="Toscana" />
                </div>
                <div class="carousel-slide">
                    <img src="Media/zonacero.svg" alt="Zona Cero" />
                </div>
                <div class="carousel-slide">
                    <img src="Media/kinich.svg" alt="Kinich" />
                </div>
                <div class="carousel-slide">
                    <img src="Media/humboldt.svg" alt="Humboldt" />
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contacto Section -->
<section id="contacto" class="section">
    <div class="content-wrapper">
        <div class="contact-card">
            <div class="social-panel">
                <h3>Nuestras Redes</h3>
                <p>Encuéntranos y síguenos para no perderte ninguna novedad.</p>
                <div class="social-links-container">
                    <div class="social-link">
                        <i class='bx bxl-instagram' style="color: #E4405F;"></i>
                        <span>Instagram</span>
                    </div>
                    <div class="social-link">
                        <i class='bx bxl-whatsapp' style="color: #25D366;"></i>
                        <span>WhatsApp</span>
                    </div>
                    <div class="social-link">
                        <i class='bx bxl-linkedin' style="color: #0A66C2;"></i>
                        <span>LinkedIn</span>
                    </div>
                </div>
            </div>

            <div class="form-panel">
                <h3>Envíanos un Mensaje</h3>
                <form>
                    <div class="form-group"><label for="name">Nombre</label><input id="name" type="text" class="form-input"></div>
                    <div class="form-group"><label for="email">Email</label><input id="email" type="email" class="form-input"></div>
                    <div class="form-group"><label for="message">Mensaje</label><textarea id="message" class="form-input" rows="4"></textarea></div>
                    <div class="form-group"><button type="submit" class="form-button">Enviar</button></div>
                </form>
            </div>
        </div>
    </div>
</section>

<footer class="footer">
    <div class="footer-contenido">
      <div class="footer-col">
        <h3>Legal</h3>
        <ul>
          <li><a href="Documentos/Aviso de privacidad.pdf" target="_blank">Aviso de privacidad</a></li>
          <li><a href="Documentos/Terminos y condiciones.pdf" target="_blank">Términos y condiciones</a></li>
        </ul>
      </div>
      <div class="footer-col">
        <h3>Contacto</h3>
        <ul>
          <li><i class='bx bx-envelope'></i> <a href="mailto:<EMAIL>"><EMAIL></a></li>
          <li><i class='bx bx-phone'></i> <a href="tel:+522228227500">+52 2228227500</a></li>
          <li><i class='bx bx-map'></i> <a href="https://maps.app.goo.gl/6sn6PzuURPvnMgoi7?g_st=aw" target="_blank">Sexto Retorno de Circuito de Osa Menor 43-3-2D<br>Ciudad Judicial<br>72810 San Andrés Cholula, Pue.</a></li>
        </ul>
      </div>
  
      <div class="footer-col">
        <h3>Redes</h3>
        <ul class="redes">
          <li><a href="https://www.facebook.com/profile.php?id=61577800024997&locale=es_ES" target="_blank"><i class='bx bxl-facebook'></i> Facebook</a></li>
          <li><a href="https://www.instagram.com/aletheia__solutions/" target="_blank"><i class='bx bxl-instagram'></i> Instagram</a></li>
          <li><a href="https://x.com/AISolutionsmx?t=7grr2ZxbrMG_rCMWzkM0lw&s=08" target="_blank"><i class='bx bxl-twitter'></i> Twitter/X</a></li>
        </ul>
      </div>
    </div>
    <div class="copy">
        <p>© 2025 Aletheia Solutions. Todos los derechos reservados.</p>
    </div>
</footer>

<script src="index.js" defer></script>
</body>
</html>

</body>
</html>
