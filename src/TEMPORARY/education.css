/* ===================================================================
   EDUCATION SUBPAGE STYLES (education.html)
   Includes shared styles from shared.css
   =================================================================== */

/* ===================================================================
   CSS VARIABLES & ROOT STYLES (from shared.css)
   =================================================================== */
:root {
    /* New Light Theme Color Palette */
    --primary-accent: #5E6666;      /* Dark gray-cyan for primary interactive elements */
    --secondary-accent: #AEC3C3;    /* Muted light cyan/gray for secondary accents */
    --background-light: #FFFFFF;    /* Main white background */
    --background-soft: #F8F9FA;     /* Soft off-white for alternate sections */
    --text-dark: #333737;           /* Main dark text color for readability */
    --text-light: #F2F2F2;          /* Light gray for text on dark backgrounds */
    --border-color: #E0E0E0;        /* Light gray for borders and dividers */

    /* Glassmorphism for Light Theme */
    --glass-bg: rgba(255, 255, 255, 0.6);
    --glass-blur: blur(10px);

    /* DYNAMIC STARRY BACKGROUND VARIABLE */
    --starry-background:
        radial-gradient(1px 1px at 50px 100px, #888, transparent),
        radial-gradient(1px 1px at 100px 200px, #999, transparent),
        radial-gradient(1.5px 1.5px at 250px 350px, #aaa, transparent);

    /* Typography */
    --font-primary: 'Inter', 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

    /* Responsive breakpoints */
    --mobile: 768px;
    --tablet: 1024px;
    --desktop: 1200px;
}

/* Utility: Starry background for dark sections only */
.starfield {
    background-image:
        var(--starry-background),
        linear-gradient(to bottom, #2a2f2f, #1a1a1a);
    background-size:
        400px 400px,
        300px 300px,
        600px 600px,
        100% 100%;
    background-repeat: repeat, repeat, repeat, no-repeat;
    background-attachment: fixed;
}

/* ===================================================================
   BASE STYLES & RESETS (from shared.css)
   =================================================================== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: var(--font-primary);
    background-color: var(--background-light);
    color: var(--text-dark);
    line-height: 1.6;
    overflow-x: hidden;
    font-feature-settings: 'rlig' 1, 'calt' 1;
}

/* Accessibility improvements */
button, 
.cta-button, 
.mas-info-btn, 
.form-button,
.nav-link {
    min-height: 44px;
    min-width: 44px;
}

button:focus,
.cta-button:focus,
.mas-info-btn:focus,
.form-button:focus,
.nav-link:focus,
.form-input:focus {
    outline: 2px solid var(--primary-accent);
    outline-offset: 2px;
}

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===================================================================
   COMMON LAYOUT COMPONENTS (from shared.css)
   =================================================================== */
.section {
    padding: 4rem 2rem;
    position: relative;
}

.content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title h2 {
    font-size: clamp(1.8rem, 1.2rem + 2vw, 2.5rem);
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.section-title p {
    font-size: 1.1rem;
    color: var(--text-dark);
    opacity: 0.8;
    max-width: 600px;
    margin: 0 auto;
}

.philosophy-intro {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-dark);
    opacity: 0.9;
    margin-bottom: 2.5rem;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* ===================================================================
   HEADER (from shared.css)
   =================================================================== */
.aurora-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 70px;
    z-index: 1000;
    transition: all 0.3s ease;
    backdrop-filter: var(--glass-blur);
    background-color: #FFFFFF;
    border-bottom: 1px solid var(--border-color);
}

/* Transparent state over hero */
.aurora-header.is-transparent {
    background-color: transparent;
    border-bottom-color: transparent;
}

.aurora-header.is-transparent .nav-link {
    color: #FFFFFF;
}

.aurora-header.is-transparent .nav-link::before {
    background: #FFFFFF;
}

.aurora-header.is-transparent .nav-highlight {
    color: #FFFFFF;
    border-color: rgba(255, 255, 255, 0.8);
}

.aurora-header.is-transparent .mobile-menu-toggle span {
    background-color: #FFFFFF;
}

/* Spacer shown only when header is solid to avoid layout jump */
.header-spacer {
    height: 70px;
    display: none;
}

.aurora-header.is-solid + .header-spacer {
    display: block;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

.logo {
    height: 40px;
    width: auto;
}

.nav-menu {
    display: flex;
    gap: 2.5rem;
    align-items: center;
}

.nav-link {
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-dark);
    text-decoration: none;
    padding: 0.5rem 0;
    position: relative;
    transition: all 0.3s ease;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-accent);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::before {
    width: 100%;
}

.nav-highlight {
    color: var(--text-dark);
    padding: 0.6rem 1.2rem;
    border-radius: 6px;
    font-weight: 600;
    border: 1px solid var(--primary-accent);
    border-bottom: 2px solid var(--primary-accent);
}

.nav-highlight:hover {
    transform: translateY(-2px);
    border-bottom: 2px solid var(--primary-accent);
}

.nav-highlight::before {
    display: none;
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 1001;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--text-dark);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* ===================================================================
   COMMON BUTTON STYLES (from shared.css)
   =================================================================== */
.cta-button {
    display: inline-block;
    padding: 0.9rem 1.8rem;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-align: center;
    cursor: pointer;
    min-width: 140px;
    color: #1a1a1a;
    border: 2px solid transparent;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(174, 195, 195, 0.3);
}

.cta-primary {
    background: var(--secondary-accent);
    color: var(--text-dark);
    border-color: var(--secondary-accent);
}

.cta-secondary {
    background: transparent;
    color: var(--text-dark);
    border-color: var(--text-dark);
}

.cta-secondary:hover {
    background: var(--text-dark);
    color: var(--background-light);
}

.mas-info-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: transparent;
    color: var(--text-dark);
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    justify-content: center;
    align-self: center;
    position: relative;
    overflow: hidden;
}

.mas-info-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(174, 195, 195, 0.2), transparent);
    transition: left 0.5s ease;
}

.mas-info-btn:hover {
    background: var(--secondary-accent);
    color: var(--text-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(174, 195, 195, 0.3);
    border-color: var(--secondary-accent);
}

.mas-info-btn:hover::before {
    left: 100%;
}

.mas-info-btn i {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.mas-info-btn:hover i {
    transform: translateX(3px);
}

.form-button {
    width: 100%;
    padding: 1.2rem;
    border: none;
    border-radius: 8px;
    background: var(--primary-accent);
    color: var(--background-light);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.form-button:hover {
    background: #4a5050;
    transform: translateY(-2px);
}

/* ===================================================================
   FOOTER (from shared.css)
   =================================================================== */
.footer {
    padding: 4rem 2rem 3rem;
    color: var(--text-dark);
    background-color: var(--background-soft);
    border-top: 1px solid var(--border-color);
}

.footer-contenido {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    flex-wrap: wrap;
    gap: 3rem;
    padding: 0 1rem;
}

.footer-col h3 {
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    font-size: 1rem;
    font-weight: 600;
}

.footer-col ul {
    list-style: none;
    padding: 0;
}

.footer-col ul li {
    margin-bottom: 1rem;
    line-height: 1.5;
}

.footer-col ul li a {
    text-decoration: none;
    color: var(--text-dark);
    opacity: 0.8;
    transition: opacity 0.3s ease;
    display: inline-block;
    padding: 0.2rem 0;
}

.footer-col ul li a:hover {
    opacity: 1;
    color: var(--primary-accent);
}

.copy {
    width: 100%;
    max-width: 1200px;
    margin: 3rem auto 0 auto;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
    opacity: 0.7;
    padding-left: 1rem;
    padding-right: 1rem;
}

/* ===================================================================
   RESPONSIVE DESIGN - SHARED COMPONENTS (from shared.css)
   =================================================================== */

/* Mobile devices */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: left 0.3s ease;
        z-index: 999;
    }

    /* Ensure readable colors when header is transparent but mobile menu is open */
    .aurora-header.is-transparent .nav-menu,
    .aurora-header.is-transparent .nav-menu.active {
        background: rgba(255, 255, 255, 0.95);
    }

    .aurora-header.is-transparent .nav-menu .nav-link {
        color: var(--text-dark);
    }

    .aurora-header.is-transparent .nav-menu .nav-link::before {
        background: var(--primary-accent);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu .nav-link {
        margin: 1rem 0;
        font-size: 1.2rem;
        padding: 1rem 2rem;
        border-radius: 8px;
        width: 80%;
        text-align: center;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .aurora-header.is-transparent .nav-menu .nav-highlight {
        background: var(--primary-accent);
        color: #FFFFFF;
        border-color: var(--primary-accent);
    }

    .nav-menu .nav-highlight {
        background: var(--primary-accent);
        color: white;
        border-color: var(--primary-accent);
    }

    .section {
        padding: 3rem 1rem;
    }

    .content-wrapper {
        padding: 0 0.5rem;
    }

    .header-container {
        padding: 0.5rem 1rem;
    }

    .cta-button {
        min-width: 140px;
        padding: 0.8rem 1.5rem;
    }

    .mas-info-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.8rem;
        margin-top: 1rem;
    }

    .footer {
        padding: 3rem 1rem 2rem;
    }

    .footer-contenido {
        gap: 2rem;
        flex-direction: column;
    }

    .copy {
        margin: 2rem auto 0 auto;
        padding: 0 0.5rem;
    }
}

/* Small mobile devices */
@media (max-width: 480px) {
    .nav-menu .nav-link {
        font-size: 1.1rem;
        padding: 0.8rem 1.5rem;
        width: 90%;
    }

    .section {
        padding: 2rem 0.5rem;
    }

    .content-wrapper {
        padding: 0 0.25rem;
    }

    .cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
        min-width: 120px;
    }

    .mas-info-btn {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
        margin-top: 0.8rem;
    }

    .form-input, .form-button {
        padding: 1rem;
    }
}

/* ===================================================================
   EDUCATION-SPECIFIC STYLES
   =================================================================== */

.edu-hero {
    /* Fill the visible viewport minus fixed header height on all devices */
    min-height: calc(100svh - 70px);
    margin-top: 70px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 2rem;
}

/* Next section after hero */
.edu-next {
    /* let global .section handle spacing */
    height: auto;
}

.edu-container {
    width: 100%;
    max-width: 1200px;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

 

.edu-top {
    display: grid;
    grid-template-columns: minmax(0, 1fr) minmax(0, 1.4fr);
    gap: 2rem;
    align-items: stretch;
}

.edu-left-square {
    background: url('Media/welcomedu.png') center/contain no-repeat;
    width: 100%;
    height: 100%;
    min-height: 300px;
    aspect-ratio: 1 / 1;
}

.edu-right-rect {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 2rem 0 2rem 2rem;
}

.edu-title {
    font-size: clamp(1.75rem, 1.1rem + 2.5vw, 2.6rem);
    font-weight: 800;
    color: var(--text-dark);
    text-transform: uppercase;
    margin-bottom: 1rem;
}

.edu-desc {
    font-size: 1.05rem;
    color: var(--text-dark);
    opacity: 0.9;
    line-height: 1.6;
}

.edu-bottom {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    align-items: start;
}

.edu-tabs { 
    display: flex; 
    flex-direction: column; 
    gap: 1rem; 
}

.edu-tablist {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    border-bottom: 2px solid var(--border-color);
}

.edu-tab {
    appearance: none;
    background: transparent;
    border: 1px solid transparent;
    border-bottom: none;
    padding: 0.6rem 0.9rem;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    font-weight: 600;
    color: var(--text-dark);
    transition: background-color 0.2s ease, border-color 0.2s ease;
}

.edu-tab[aria-selected="true"] {
    background: var(--background-light);
    border-color: var(--border-color);
    border-bottom-color: transparent;
}

.edu-tab:focus-visible { 
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

.edu-tabpanel { 
    display: none;
    padding: 0; 
    background: transparent; 
    border: none; 
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.edu-tabpanel.active {
    display: block;
}

/* Smooth transitions for all interactive elements */
.edu-tab,
.edu-card details,
.edu-card summary {
    transition: all 0.2s ease;
}

.edu-tabcontent-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.25rem;
    align-items: start;
}

/* Keep panel height stable on desktop to avoid layout shift when opening items */
@media (min-width: 901px) {
  :root { --tab-panel-height: clamp(360px, 60vh, 560px); }
  .edu-tabcontent-grid { grid-template-rows: var(--tab-panel-height); }
  .edu-card { height: 100%; }
  .edu-card {
    display: flex;
    flex-direction: column;
    overflow: hidden; /* contain inner scrolling */
  }
  .edu-card h3 {
    position: sticky;
    top: 0;
    background: var(--background-light);
    z-index: 1;
    padding-top: 0.25rem;
    margin-top: -0.25rem; /* optical align */
  }
  .edu-card ul {
    flex: 1;
    overflow: auto;
    padding-right: 6px; /* room for scrollbar */
  }
  /* Subtle scrollbar to match theme */
  .edu-card ul::-webkit-scrollbar { width: 8px; }
  .edu-card ul::-webkit-scrollbar-track { background: transparent; }
  .edu-card ul::-webkit-scrollbar-thumb { background: rgba(0,0,0,0.12); border-radius: 8px; }
}

/* On mobile, contain accordion growth to avoid pushing other sections */
@media (max-width: 900px) {
  .edu-card {
    max-height: 65vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .edu-card ul { flex: 1; overflow: auto; }
}

/* Enhanced mobile tab styling */
@media (max-width: 768px) {
  .edu-tablist {
    flex-direction: column;
    gap: 0.25rem;
    border-bottom: none;
    border-left: 2px solid var(--border-color);
    padding-left: 0.5rem;
    margin-bottom: 1rem;
  }
  
  .edu-tab {
    border: 1px solid var(--border-color);
    border-left: none;
    border-radius: 0 8px 8px 0;
    text-align: left;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
  }
  
  .edu-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-accent, #5E6666);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: -1;
  }
  
  .edu-tab[aria-selected="true"] {
    color: white;
    border-color: var(--primary-accent, #5E6666);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    background: var(--primary-accent, #5E6666);
  }
  
  .edu-tab[aria-selected="true"]::before {
    transform: translateX(0);
    opacity: 0; /* Hide the pseudo-element since we're using direct background */
  }
  
  .edu-tab:not([aria-selected="true"]):hover {
    background: rgba(0,0,0,0.05);
    transform: translateX(2px);
  }
  
  .edu-tabpanel {
    margin-top: 1rem;
    padding: 0;
  }
  
  .edu-tabcontent-grid {
    gap: 1rem;
  }
  
  .edu-card {
    padding: 1.25rem;
    max-height: none;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
  
  .edu-card details {
    margin-bottom: 0.5rem;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .edu-card summary {
    padding: 12px 40px 12px 16px;
    font-size: 0.95rem;
    cursor: pointer;
    user-select: none;
  }
  
  .edu-card summary:hover {
    background: rgba(0,0,0,0.03);
  }
  
  .edu-point-details {
    padding: 12px 16px 16px 20px;
    font-size: 0.9rem;
    line-height: 1.5;
  }
  
  /* Improve accordion arrow animation */
  .edu-card summary::after {
    transition: transform 0.3s ease;
  }
  
  .edu-card details[open] summary::after { 
    transform: translateY(-50%) rotate(45deg); 
  }
}

@media (max-width: 480px) {
  .edu-tab {
    padding: 0.6rem 0.8rem;
    font-size: 0.9rem;
    min-height: 44px; /* Ensure touch target is large enough */
  }
  
  .edu-card {
    padding: 1rem;
    margin: 0 0.5rem;
  }
  
  .edu-card summary {
    padding: 10px 36px 10px 14px;
    font-size: 0.9rem;
    min-height: 44px; /* Ensure touch target is large enough */
    display: flex;
    align-items: center;
  }
  
  .edu-point-details {
    padding: 10px 14px 14px 18px;
    font-size: 0.85rem;
  }
  
  /* Improve tab list spacing on very small screens */
  .edu-tablist {
    padding-left: 0.25rem;
    gap: 0.2rem;
  }
  
  /* Ensure content doesn't overflow on small screens */
  .edu-container {
    padding: 0 0.5rem;
  }
  
  .edu-next {
    padding: 1rem 0.5rem;
  }
}

/* Ensure smooth scrolling on mobile */
@media (max-width: 768px) {
  html {
    scroll-behavior: smooth;
  }
  
  .edu-tabs {
    scroll-margin-top: 1rem;
  }
}

.edu-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 1.5rem;
}

.edu-card {
    border-radius: 12px;
    background: var(--background-light);
    padding: 1.5rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    text-decoration: none;
}

.edu-card h3 { font-size: 1.2rem; margin-bottom: 0.5rem; }
.edu-card p { font-size: 0.95rem; opacity: 0.9; }

/* Simplified accordion styling */
.edu-card ul { 
    list-style: none; 
    padding-left: 0; 
    margin: 0; 
}

.edu-card li { 
    list-style: none;
    margin: 0;
}

.edu-card li + li { 
    margin-top: 8px; 
}

.edu-card details {
    border: 1px solid var(--border-color);
    border-radius: 10px;
    background: var(--background-light);
    transition: border-left-color 0.2s ease;
}

.edu-card summary {
    cursor: pointer;
    font-weight: 600;
    line-height: 1.45;
    padding: 10px 36px 10px 12px;
    border-radius: 10px;
    position: relative;
    transition: background-color 0.2s ease;
}

.edu-card summary::-webkit-details-marker { 
    display: none; 
}

.edu-card summary::after {
    content: '';
    position: absolute;
    right: 12px;
    top: 50%;
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--text-dark);
    border-bottom: 2px solid var(--text-dark);
    transform: translateY(-50%) rotate(-45deg);
    transition: transform 0.2s ease;
}

.edu-card details[open] summary::after { 
    transform: translateY(-50%) rotate(45deg); 
}

.edu-card details[open] summary { 
    background: rgba(0,0,0,0.04); 
}

.edu-card details[open] { 
    border-left: 2px solid rgba(0,0,0,0.12); 
}

/* Simple content reveal without complex animations */
.edu-point-details { 
    padding: 8px 12px 12px 24px; 
    color: #555; 
    font-size: 0.95rem; 
}

.edu-article-card {
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    background: var(--background-light);
}

.edu-article-card h3 { font-size: 1.2rem; margin-bottom: 0.5rem; }
.edu-article-card p { font-size: 0.95rem; opacity: 0.9; }

/* Increase vertical spacing between components inside the article */
.edu-article-card > * + * {
    margin-top: 1.25rem;
}

/* Tweak list styling inside the article card under AI Assistant */
.edu-article-card ul {
    list-style: none;
    padding-left: 0;
    margin-top: 0.75rem;
}

.edu-article-card li {
    list-style: none;
    margin: 0;
}

/* AI use cases: render three rectangular cards in a row, responsive */
.edu-article-card .ai-usecases {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1.25rem;
    list-style: none;
    padding: 0;
    margin-top: 1.25rem;
}

.edu-article-card .ai-usecases li {
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem 1.25rem;
    background: var(--background-light);
}

/* Dark card variant when inside starry background */
.starry-bg .ai-usecases li {
    border-color: rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.06);
}

@media (max-width: 900px) {
    .edu-article-card .ai-usecases {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (max-width: 600px) {
    .edu-article-card .ai-usecases {
        grid-template-columns: 1fr;
    }
}

 

/* New Sections */
.edu-new-section-1, .edu-new-section-2 {
    /* use global section spacing and wrappers */
    height: auto;
}

/* Two-column layout for Analytics section */
.edu-new-section-2 .content-wrapper {
    display: grid;
    grid-template-columns: 1.25fr 1fr;
    grid-template-areas:
        "title image"
        "copy  image";
    align-items: center;
    gap: 1.5rem 2rem;
}

.edu-new-section-2 .section-title {
    grid-area: title;
    text-align: left;
    margin-bottom: 0; /* handled by grid gap */
}

.edu-new-section-2 .philosophy-intro {
    grid-area: copy;
    margin: 0; /* handled by grid gap */
}

.edu-new-section-2 .edu-analytics-graphic {
    grid-area: image;
    justify-self: end;
    width: 100%;
    max-width: 520px;
    aspect-ratio: 1 / 1; /* make it square */
    height: auto;
    object-fit: contain;
    margin: 0;
}

/* Responsive */
@media (max-width: 1024px) {
    .edu-top { grid-template-columns: 1fr; }
    .edu-right-rect { padding: 1.5rem; }
    .edu-bottom { grid-template-columns: 1fr; }
    .edu-left-square { min-height: 260px; }
}

@media (max-width: 768px) {
    .edu-hero { padding: 0 1rem; }
    .edu-next { padding: 2rem 1rem; }
    /* auto-fit handles columns; keep for legacy browsers */
    .edu-cards-grid { grid-template-columns: 1fr; }
    .edu-title { font-size: 2rem; }
    .edu-left-square { min-height: 220px; }
}

/* Responsive stacking for Analytics section */
@media (max-width: 900px) {
    .edu-new-section-2 .content-wrapper {
        grid-template-columns: 1fr;
        grid-template-areas:
            "title"
            "copy"
            "image";
    }
    .edu-new-section-2 .section-title { text-align: center; }
    .edu-new-section-2 .edu-analytics-graphic {
        justify-self: center;
        max-width: 420px;
    }
}

/* Gallery for Capacitación section */
.edu-gallery {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-top: 2rem;
}

.edu-gallery-item {
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border-color);
    background: var(--background-light);
}

.edu-gallery-item img {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.edu-gallery-item:hover img {
    transform: scale(1.04);
}

@media (max-width: 1024px) {
    .edu-gallery { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 600px) {
    .edu-gallery { grid-template-columns: 1fr; }
}

/* Analytics graphic sizing */
.edu-analytics-graphic {
    display: block;
    width: min(90%, 720px);
    height: auto;
    margin: 1.5rem auto 0;
}

@media (max-width: 768px) {
    .edu-analytics-graphic { width: min(95%, 520px); }
}

@media (max-width: 420px) {
    .edu-analytics-graphic { width: 92%; }
}

/* Starry background variant for focused article card */
.starry-bg {
    /* Override default card background and border */
    background-color: #1a1a1a !important;
    border: 1px solid rgba(255, 255, 255, 0.25) !important;

    /* Starry background (scoped to element) */
    background-image:
        radial-gradient(1px 1px at 50px 100px, white, transparent),
        radial-gradient(1px 1px at 100px 200px, white, transparent),
        radial-gradient(1.5px 1.5px at 250px 350px, #ddd, transparent);
    background-size:
        400px 400px,
        300px 300px,
        600px 600px;
    background-repeat: repeat, repeat, repeat;
    background-attachment: fixed;
    color: #fff;
}

/* Force inner text and common elements to white for contrast */
.starry-bg h1,
.starry-bg h2,
.starry-bg h3,
.starry-bg p,
.starry-bg li,
.starry-bg a,
.starry-bg strong,
.starry-bg summary,
.starry-bg .edu-point-details {
    color: #fff !important;
}

.starry-bg a:hover { color: #fff; opacity: 0.9; }

/* Customer Banner */
.customer-banner {
    background-color: #1a1a1a;
    background-image:
        /* Stars */
        radial-gradient(1px 1px at 50px 100px, white, transparent),
        radial-gradient(1px 1px at 100px 200px, white, transparent),
        radial-gradient(1.5px 1.5px at 250px 350px, #ddd, transparent);
    background-size:
        400px 400px,
        300px 300px,
        600px 600px;
    background-repeat: repeat, repeat, repeat;
    background-attachment: fixed;
    padding: 3rem 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.customer-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.customer-banner .banner-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    gap: 1.5rem;
    position: relative;
    z-index: 2;
}

.customer-banner p {
    margin: 0;
    font-weight: 500;
    color: #ffffff;
    font-size: 1.1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.customer-banner .banner-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.customer-banner .btn {
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    min-width: 140px;
    text-align: center;
}

.customer-banner .btn-secondary {
    background-color: #AEC3C3;
    color: #272727;
}

.customer-banner .btn-secondary:hover {
    background-color: #abb8b8;
    transform: translateY(-1px);
}

.customer-banner .btn-outline {
    background-color: transparent;
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.customer-banner .btn-outline:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: #ffffff;
}

@media (max-width: 768px) {
    .customer-banner {
        padding: 2rem 1rem;
    }
    
    .customer-banner .banner-content {
        gap: 1rem;
    }
    
    .customer-banner .banner-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .customer-banner .btn {
        width: 100%;
        max-width: 200px;
    }
}

/* Global list styling - remove all decorators */
.edu-next ul,
.edu-new-section-1 ul,
.edu-new-section-2 ul,
.footer ul {
    list-style: none;
    padding-left: 0;
    margin: 0;
}

.edu-next li,
.edu-new-section-1 li,
.edu-new-section-2 li,
.footer li {
    list-style: none;
    margin: 0;
}

/* Footer list styling - override any existing styles */
.footer ul,
.footer-col ul,
.footer .footer-col ul {
    list-style: none !important;
    padding-left: 0 !important;
    margin: 0 !important;
}

.footer li,
.footer-col li,
.footer .footer-col li {
    list-style: none !important;
    margin: 0 !important;
}

/* Ensure footer links don't have list styling */
.footer a,
.footer-col a {
    text-decoration: none;
    color: inherit;
}
