import React, { useState, useEffect } from 'react';
import {
  AuroraHeader,
  HeaderContainer,
  Logo,
  NavMenu,
  NavLink,
  NavHighlight,
  MobileMenuToggle,
  HeaderSpacer,
} from './styles';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const handleScroll = () => {
    const offset = window.scrollY;
    if (offset > 50) {
      setIsScrolled(true);
    } else {
      setIsScrolled(false);
    }
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <>
      <AuroraHeader isTransparent={!isScrolled}>
        <HeaderContainer>
          <Logo src="Media/Logo.svg" alt="Aletheia" />
          <MobileMenuToggle
            className={isMenuOpen ? 'active' : ''}
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <span />
            <span />
            <span />
          </MobileMenuToggle>
          <NavMenu isOpen={isMenuOpen}>
            <NavLink href="#soluciones">Soluciones</NavLink>
            <NavLink href="#precios">Demos</NavLink>
            <NavLink href="education.html">FAQs</NavLink>
            <NavHighlight href="#contacto">Contacto</NavHighlight>
          </NavMenu>
        </HeaderContainer>
      </AuroraHeader>
      <HeaderSpacer isSolid={isScrolled} />
    </>
  );
};

export default Header;
