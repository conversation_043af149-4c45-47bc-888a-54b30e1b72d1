import React from 'react';
import './BusinessShowcase.styles.js';

const BusinessShowcase = () => {
  const businessSectors = [
    {
      icon: "Media/small_store.svg",
      alt: "Comercio Minorista",
      title: "Comercio Minorista",
      description: "Soluciones integrales para tiendas físicas y online, incluyendo gestión de inventario, punto de venta y análisis de ventas."
    },
    {
      icon: "Media/sew.svg",
      alt: "Manufactura",
      title: "Manufactura",
      description: "Automatización de procesos productivos, control de calidad y optimización de la cadena de suministro."
    },
    {
      icon: "Media/lawyer.svg",
      alt: "Servicios Profesionales",
      title: "Servicios Profesionales",
      description: "Herramientas especializadas para consultorías, despachos legales, contables y servicios financieros."
    },
    {
      icon: "Media/school.svg",
      alt: "Instituciones Educativas",
      title: "Instituciones Educativas",
      description: "Plataformas educativas para diferentes instituciones, de todos los niveles. Con IA generativa para la creación de contenidos, evaluación y seguimiento de alumnos."
    },
    {
      icon: "Media/hotel.svg",
      alt: "Hospitalidad y Turismo",
      title: "Hospitalidad y Turismo",
      description: "Plataformas de reservas, gestión de hoteles y restaurantes con experiencias personalizadas."
    },
    {
      icon: "Media/hospital.svg",
      alt: "Salud y Bienestar",
      title: "Salud y Bienestar",
      description: "Sistemas de gestión médica, citas en línea y seguimiento de pacientes con IA."
    }
  ];

  return (
    <section className="section">
      <div className="section-title">
        <h2>PARA TODOS LOS SECTORES</h2>
      </div>
      <div className="content-wrapper">
        <div className="business-showcase">
          <div className="business-blocks">
            {businessSectors.map((sector, index) => (
              <div key={index} className="business-block">
                <div className="business-icon">
                  <img 
                    src={sector.icon} 
                    alt={sector.alt} 
                    className="business-svg-icon"
                  />
                </div>
                <div className="business-content">
                  <h3 className="business-title">{sector.title}</h3>
                  <p>{sector.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default BusinessShowcase;
