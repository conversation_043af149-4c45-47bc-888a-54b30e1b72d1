import styled from 'styled-components';

export const BusinessShowcaseSection = styled.section`
  &.section {
    padding: 2rem 0;
  }
`;

export const BusinessShowcaseContainer = styled.div`
  &.business-showcase {
    padding: 2rem 0;
  }
`;

export const BusinessBlocks = styled.div`
  &.business-blocks {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 2rem;
  }
`;

export const BusinessBlock = styled.div`
  &.business-block {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem;
    background: var(--background-light);
  }
`;

export const BusinessIcon = styled.div`
  &.business-icon {
    flex-shrink: 0;
  }
`;

export const BusinessSvgIcon = styled.img`
  &.business-svg-icon {
    width: 70px;
    height: 70px;
    object-fit: contain;
    transition: all 0.3s ease;
  }
`;

export const BusinessContent = styled.div`
  &.business-content {
    flex: 1;
  }
`;

export const BusinessTitle = styled.h3`
  &.business-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 3px solid var(--secondary-accent);
    display: inline-block;
  }
`;

export const BusinessDescription = styled.p`
  &.business-content p {
    color: var(--text-dark);
    font-size: 0.95rem;
    line-height: 1.6;
    margin: 0;
  }
`;

// Alternative approach using CSS-in-JS with styled-components
export const StyledBusinessShowcase = styled.section`
  padding: 2rem 0;
`;

export const StyledBusinessBlocks = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 2rem;
`;

export const StyledBusinessBlock = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem;
  background: var(--background-light);
`;

export const StyledBusinessIcon = styled.div`
  flex-shrink: 0;
`;

export const StyledBusinessSvgIcon = styled.img`
  width: 70px;
  height: 70px;
  object-fit: contain;
  transition: all 0.3s ease;
`;

export const StyledBusinessContent = styled.div`
  flex: 1;
`;

export const StyledBusinessTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 3px solid var(--secondary-accent);
  display: inline-block;
`;

export const StyledBusinessDescription = styled.p`
  color: var(--text-dark);
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
`;
